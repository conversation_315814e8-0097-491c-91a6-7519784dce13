/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import { StatusBar, useColorScheme } from 'react-native';
// import { useFonts } from 'expo-font';
// import {
//   Baloo2_400Regular,
//   Baloo2_500Medium,
//   Baloo2_600SemiBold,
//   Baloo2_700Bold,
//   Baloo2_800ExtraBold,
// } from '@expo-google-fonts/baloo-2';
// import {
//   Imbue_400Regular,
//   Imbue_500Medium,
//   Imbue_600SemiBold,
//   Imbue_700Bold,
//   Imbue_800ExtraBold,
//   Imbue_900Black,
// } from '@expo-google-fonts/imbue';
import {
  DarkTheme,
  DefaultTheme,
  NavigationContainer,
  ThemeProvider,
} from '@react-navigation/native';

import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { createStackNavigator } from '@react-navigation/stack';
import HomeScreen from './src/screens/Home';
import NotFoundScreen from './src/screens/NotFound';

const Stack = createStackNavigator();

function App() {
  const isDarkMode = useColorScheme() === 'dark';
  const colorScheme = useColorScheme();

  // const [loaded] = useFonts({
  //   ImbueBold: Imbue_700Bold,
  //   ImbueRegular: Imbue_400Regular,
  //   ImbueMedium: Imbue_500Medium,
  //   ImbueSemiBold: Imbue_600SemiBold,
  //   ImbueExtraBold: Imbue_800ExtraBold,
  //   ImbueBlack: Imbue_900Black,
  //   Baloo2Regular: Baloo2_400Regular,
  //   Baloo2Medium: Baloo2_500Medium,
  //   Baloo2SemiBold: Baloo2_600SemiBold,
  //   Baloo2Bold: Baloo2_700Bold,
  //   Baloo2ExtraBold: Baloo2_800ExtraBold,
  // });

  // if (!loaded) {
  //   return null;
  // }

  return (
    <SafeAreaProvider>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <ThemeProvider
          value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}
        >
          <NavigationContainer>
            <Stack.Navigator screenOptions={{ headerShown: false }}>
              <Stack.Screen name="Home" component={HomeScreen} />
              <Stack.Screen name="NotFound" component={NotFoundScreen} />
            </Stack.Navigator>
          </NavigationContainer>
          <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
        </ThemeProvider>
      </GestureHandlerRootView>
    </SafeAreaProvider>
  );
}

export default App;
