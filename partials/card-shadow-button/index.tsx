import React from 'react';
import { Text, StyleSheet, View, Pressable } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

interface ButtonProps {
  title: string;
  onPress: () => void;
}

const CardShadowButton = ({ title, onPress }: ButtonProps) => {
  return (
    <Pressable onPress={onPress} style={styles.container}>
      <LinearGradient
          colors={[
            'rgba(91, 0, 127, 1)',
            'rgba(211, 0, 110, 1)',
          ]}
          style={styles.shadowContainer}
        >
        <View style={styles.shadowContainer}>

        <View style={styles.button}>
          <Text style={styles.buttonText}>{title}</Text>
        </View>
        </View>
      </LinearGradient> 
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    maxWidth: 220,
    height: 48,
  },
  shadowContainer: {
    // flex: 1,
    borderRadius: 60,
    padding: 1, 
    overflow: 'hidden',
  },
  button: {
    backgroundColor: '#1B1B1B',
    borderRadius: 60,
    padding: 14,
  },
  buttonText: {
    color: '#F5F5F5',
    fontWeight: 400,
    lineHeight: 16,
    fontSize: 16,
    textAlign: 'center',
  },
});

export default CardShadowButton;