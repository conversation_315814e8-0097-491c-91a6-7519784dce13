import Cover from '@/assets/images/bg-crossing-lines.png';
import { FONTS } from '@constants/fonts';
import { StyleSheet, Text, View, Image } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

export const CardCrossingLines = () => {
  return (
    <LinearGradient
      colors={[
        'rgba(195, 102, 138, 1)',
        'rgba(162, 218, 253, 1)',
        'rgba(35, 172, 250, 1)',
        'rgba(15, 19, 129, 1)',
      ]}
      locations={[0, 0.07, 0.17, 0.45]}
      style={styles.container}
    >
      <View style={styles.contentContainer}>
        <View>
          <Image source={Cover} resizeMode="cover" style={styles.image} />
          <View>
            <Text style={styles.title}>Crossing Lines</Text>
            <View style={styles.descriptionContainer}>
              <Text style={styles.description}>
                When friendship starts feeling like more.
              </Text>
            </View>
          </View>
        </View>
        <View style={styles.priceContainer}>
          <Text style={styles.price}>$9.9/ 4h</Text>
          <Text style={styles.freeTrial}>Free trial</Text>
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    aspectRatio: 7 / 10,
    borderWidth: 1,
    borderColor: '#0F1381',
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 28,
    width: 266,
    height: 374,
  },
  imageContainer: {
    aspectRatio: 14 / 10,
    width: 100,
  },
  title: {
    fontSize: 40,
    fontFamily: FONTS.IMBUE.BOLD,
    color: 'rgba(255, 255, 255, 0.85)',
    textTransform: 'uppercase',
    textAlign: 'center',
  },
  descriptionContainer: {
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: 'rgba(255, 255, 255, 0.85)',
    opacity: 0.7,
    textAlign: 'center',
  },
  image: {
    aspectRatio: 14 / 10,
    width: '90%',
    marginLeft: 16,
    height: 150,
  },
  priceContainer: {
    gap: 12,
  },
  price: {
    fontSize: 14,
    fontFamily: FONTS.BALOO2.REGULAR,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.85)',
  },
  freeTrial: {
    fontSize: 24,
    fontFamily: FONTS.IMBUE.MEDIUM,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.85)',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  }
});
