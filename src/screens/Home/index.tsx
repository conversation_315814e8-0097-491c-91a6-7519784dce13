import Background from '@/assets/images/home-bg.png';
import {
  Card<PERSON>rossingLines,
  CardEmberAfterglow,
  CardFirstDate,
  CardLateNightTalk,
} from '@/partials';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import React, { useCallback, useRef } from 'react';
import {
  Dimensions,
  ImageBackground,
  StatusBar,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Rive from 'rive-react-native';
import CarouselCard from '@/src/components/CarouselCard';
import { FONTS } from '@/constants/fonts';
import CardCrossingLinesBSContent from '@src/components/CardCrossingLinesBSContent';
import CardFirstDateBSContent from '@src/components/CardFirstDateBSContent';
import CardLateNightTalkBSContent from '@src/components/CardLateNightTalkBSContent';
import CardEmberAfterglowBSContent from '@src/components/CardEmberAfterglowBSContent';

const { height: screenHeight } = Dimensions.get('window');

export default function HomeScreen() {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const insets = useSafeAreaInsets();

  const [activeSlide, setActiveSlide] = React.useState(0);
  console.log("🚀 ~ HomeScreen ~ activeSlide:", activeSlide)

  // callbacks
  const handleSheetChanges = useCallback((index: number) => {
    // console.log('handleSheetChanges', index);
  }, []);

  const handleOpen = () => {
    bottomSheetRef.current?.expand();
  };

  const handleClose = () => {
    bottomSheetRef.current?.close();
  };

  const data = [
    { id: 1, comp: CardFirstDate, onPress: handleOpen },
    { id: 2, comp: CardLateNightTalk, onPress: handleOpen },
    { id: 3, comp: CardEmberAfterglow, onPress: handleOpen },
    { id: 4, comp: CardCrossingLines, onPress: handleOpen },
  ];

  const renderBottomSheetContent = () => {
    switch (activeSlide) {
      case 0:
        return <CardFirstDateBSContent onClose={handleClose} />;
      case 1:
        return <CardLateNightTalkBSContent onClose={handleClose} />;
      case 2:
        return <CardEmberAfterglowBSContent onClose={handleClose} />;
      case 3:
        return <CardCrossingLinesBSContent onClose={handleClose} />;
      default:
        return null;
    }
  };

  return (
    <>
      <StatusBar barStyle="light-content" />
      <ImageBackground
        source={Background}
        style={[
          styles.container,
          { paddingTop: insets.top, paddingBottom: insets.bottom },
        ]}
      >
        <View style={styles.fireContainer}>
          <Rive
            resourceName="heart_brigde"
            style={{ width: 60, height: 60 }}
            autoplay
          />
        </View>
        <View style={styles.cardContainer}>
          <CarouselCard data={data} setActiveSlide={(activeSlide) => {
            setActiveSlide(activeSlide);
          }} />
        </View>
        <View style={styles.navigationContainer}>
          <View style={styles.navigationButtonContainer}>
            <Text style={[styles.navigationText, styles.activeText]}>
              Single
            </Text>
            <Text style={[styles.navigationText]}>Multiple</Text>
          </View>
        </View>
      </ImageBackground>
      <BottomSheet
        ref={bottomSheetRef}
        onChange={handleSheetChanges}
        snapPoints={['50%', '95%']}
        backgroundStyle={{ backgroundColor: 'transparent' }}
        handleComponent={null}
        enableHandlePanningGesture={false}
        index={-1}
        enableDynamicSizing={false}
      >
        <BottomSheetView style={styles.bsContentContainer}>
          {renderBottomSheetContent()}
        </BottomSheetView>
      </BottomSheet>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0B1713',
    justifyContent: 'space-between',
  },
  cardContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bsContentContainer: {
    flex: 1,
    height: screenHeight * 0.95,
  },
  fireContainer: {
    marginBottom: 100,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  image: {
    width: 100,
    height: 100,
  },
  navigationContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  navigationButtonContainer: {
    width: '60%',
    flexDirection: 'row',
    backgroundColor: '#313131B2',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 32,
    borderRadius: 60,
  },
  navigationText: {
    flex: 1,
    textAlign: 'center',
    fontSize: 16,
    fontFamily: FONTS.BALOO2.REGULAR,
    fontWeight: 500,
    color: '#4A4A4A',
  },
  activeText: {
    color: '#fff',
  },
});
